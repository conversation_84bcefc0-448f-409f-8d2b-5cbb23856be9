/**
 * Bulk Messaging Page Component - Profile-based messaging
 */

import React, { useState, useEffect } from 'react';
import {
  Card, Form, Input, Select, Button, Space, Upload, Table, Progress,
  message, Row, Col, InputNumber, Switch, Tag, Modal, Divider, Tooltip,
  Alert, List, Statistic, Steps
} from 'antd';
import {
  MessageOutlined, UploadOutlined, PlayCircleOutlined, StopOutlined,
  EyeOutlined, DownloadOutlined, DeleteOutlined, ReloadOutlined,
  UserOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined,
  FileExcelOutlined, SendOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

const BulkMessaging = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [profiles, setProfiles] = useState([]);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [fileValidation, setFileValidation] = useState(null);
  const [activeTask, setActiveTask] = useState(null);
  const [taskProgress, setTaskProgress] = useState({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [selectedTaskResults, setSelectedTaskResults] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    loadInitialData();

    // Set up polling for active tasks
    const interval = setInterval(() => {
      if (activeTask) {
        pollTaskStatus(activeTask);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [activeTask]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Load profiles
      const profilesData = await apiService.getProfiles();
      setProfiles(profilesData.filter(p => p.facebook_logged_in));

    } catch (error) {
      message.error('Failed to load data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      const result = await apiService.post('/api/messaging/bulk/validate-excel', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (result.valid) {
        setUploadedFile({
          file: file,
          file_path: result.file_path || file.name,
          total_recipients: result.total_recipients,
          url_type_breakdown: result.url_type_breakdown
        });
        setFileValidation(result);
        setCurrentStep(1);
        message.success(`Validated ${result.total_recipients} recipients successfully`);
      } else {
        message.error('File validation failed: ' + result.error);
        setFileValidation(result);
      }

      return false; // Prevent default upload behavior
    } catch (error) {
      message.error('Failed to validate file: ' + error.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleStartBulkMessaging = async (values) => {
    try {
      setLoading(true);

      const config = {
        name: values.name,
        sender_profile_id: values.sender_profile_id,
        excel_file_path: uploadedFile?.file_path,
        message_content: values.message_content,
        message_type: values.message_type || 'text',
        delay_between_messages_min: values.delay_between_messages_min || 10,
        delay_between_messages_max: values.delay_between_messages_max || 30,
        scroll_distance_min: values.scroll_distance_min || 200,
        scroll_distance_max: values.scroll_distance_max || 800,
        reading_time_min: values.reading_time_min || 2.0,
        reading_time_max: values.reading_time_max || 5.0,
        stop_on_consecutive_failures: values.stop_on_consecutive_failures || 3,
        skip_sent_recipients: values.skip_sent_recipients !== false
      };

      const result = await apiService.post('/api/messaging/bulk/start', { config });

      setActiveTask(result.task_id);
      setCurrentStep(2);
      message.success('Bulk messaging task started successfully');

      // Reset form
      form.resetFields();
      setUploadedFile(null);
      setFileValidation(null);
      setCurrentStep(0);

    } catch (error) {
      message.error('Failed to start bulk messaging: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const pollTaskStatus = async (taskId) => {
    try {
      const status = await apiService.get(`/api/messaging/bulk/status/${taskId}`);
      setTaskProgress(prev => ({ ...prev, [taskId]: status }));

      // Stop polling if task is completed
      if (['completed', 'failed', 'cancelled'].includes(status.status)) {
        setActiveTask(null);
        setCurrentStep(0);
      }

    } catch (error) {
      console.error('Failed to poll task status:', error);
    }
  };

  const handleStopTask = async (taskId) => {
    try {
      await apiService.post(`/api/messaging/bulk/stop/${taskId}`);
      message.success('Task stopped successfully');
      setActiveTask(null);
      setCurrentStep(0);
    } catch (error) {
      message.error('Failed to stop task: ' + error.message);
    }
  };

  const handleViewResults = async (taskId) => {
    try {
      const results = await apiService.get(`/api/messaging/bulk/results/${taskId}`);
      setSelectedTaskResults(results);
      setPreviewModalVisible(true);
    } catch (error) {
      message.error('Failed to load results: ' + error.message);
    }
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      pending: { color: 'default', text: 'Pending' },
      running: { color: 'processing', text: 'Running' },
      completed: { color: 'success', text: 'Completed' },
      failed: { color: 'error', text: 'Failed' },
      cancelled: { color: 'default', text: 'Cancelled' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const steps = [
    {
      title: 'Upload Excel',
      description: 'Upload recipient list',
      icon: <FileExcelOutlined />
    },
    {
      title: 'Configure',
      description: 'Set message and options',
      icon: <MessageOutlined />
    },
    {
      title: 'Send Messages',
      description: 'Monitor progress',
      icon: <SendOutlined />
    }
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <Col>
          <h1>Bulk Messaging (Profile-based)</h1>
          <p>Send messages to users via their profile pages with human-like behavior</p>
        </Col>
        <Col>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadInitialData}
            loading={loading}
          >
            Refresh
          </Button>
        </Col>
      </Row>

      {/* Progress Steps */}
      <Card style={{ marginBottom: 24 }}>
        <Steps current={currentStep} items={steps} />
      </Card>

      <Row gutter={[16, 16]}>
        {/* Step 1: File Upload */}
        <Col xs={24} lg={12}>
          <Card title="Step 1: Upload Recipient List">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="Excel File Format"
                description="Your Excel file should have 2 columns: 'full_name' and 'profile_url'. Profile URLs should be Facebook profile URLs or paths like '/groups/123/user/456'."
                type="info"
                showIcon
              />

              <Upload
                beforeUpload={handleFileUpload}
                accept=".csv,.xlsx,.xls"
                showUploadList={false}
                disabled={loading}
              >
                <Button 
                  icon={<UploadOutlined />} 
                  loading={loading}
                  size="large"
                  block
                >
                  Upload Excel/CSV File
                </Button>
              </Upload>

              {fileValidation && (
                <div>
                  {fileValidation.valid ? (
                    <Alert
                      message="File Validated Successfully"
                      description={
                        <div>
                          <p><strong>Total Recipients:</strong> {fileValidation.total_recipients}</p>
                          {fileValidation.url_type_breakdown && (
                            <div>
                              <p><strong>URL Types:</strong></p>
                              <ul>
                                <li>Groups: {fileValidation.url_type_breakdown.groups}</li>
                                <li>Profile PHP: {fileValidation.url_type_breakdown.profile_php}</li>
                                <li>People: {fileValidation.url_type_breakdown.people}</li>
                                <li>Direct Username: {fileValidation.url_type_breakdown.direct_username}</li>
                                <li>Other: {fileValidation.url_type_breakdown.other}</li>
                              </ul>
                            </div>
                          )}
                        </div>
                      }
                      type="success"
                      showIcon
                    />
                  ) : (
                    <Alert
                      message="File Validation Failed"
                      description={fileValidation.error}
                      type="error"
                      showIcon
                    />
                  )}

                  {fileValidation.preview && fileValidation.preview.length > 0 && (
                    <div style={{ marginTop: 16 }}>
                      <h4>Preview (First 5 rows):</h4>
                      <Table
                        size="small"
                        dataSource={fileValidation.preview}
                        columns={[
                          { title: 'Row', dataIndex: 'row', key: 'row' },
                          { 
                            title: 'Data', 
                            dataIndex: 'data', 
                            key: 'data',
                            render: (data) => (
                              <pre style={{ fontSize: '12px', margin: 0 }}>
                                {JSON.stringify(data, null, 2)}
                              </pre>
                            )
                          }
                        ]}
                        pagination={false}
                      />
                    </div>
                  )}
                </div>
              )}
            </Space>
          </Card>
        </Col>

        {/* Step 2: Configuration */}
        <Col xs={24} lg={12}>
          <Card title="Step 2: Configure Messaging">
            <Alert
              message="Messaging Workflow"
              description={
                <div>
                  <p><strong>Automated messaging follows this human-like workflow:</strong></p>
                  <ol>
                    <li>Visit recipient's profile page</li>
                    <li>Scroll down to simulate viewing profile</li>
                    <li>Scroll back up</li>
                    <li>Click message button</li>
                    <li>Type message with human-like behavior</li>
                    <li>Send message</li>
                  </ol>
                  <p><em>Uses XPath selectors from chat.html for accurate element targeting.</em></p>
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Form
              form={form}
              layout="vertical"
              onFinish={handleStartBulkMessaging}
            >
              <Form.Item
                name="name"
                label="Task Name"
                rules={[{ required: true, message: 'Please enter task name' }]}
              >
                <Input placeholder="Enter task name" />
              </Form.Item>

              <Form.Item
                name="sender_profile_id"
                label="Sender Profile"
                rules={[{ required: true, message: 'Please select sender profile' }]}
              >
                <Select placeholder="Select profile to send messages from">
                  {profiles.map(profile => (
                    <Option key={profile.id} value={profile.id}>
                      {profile.name} ({profile.facebook_username})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="message_content"
                label="Message Content"
                rules={[{ required: true, message: 'Please enter message content' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="Enter your message content..."
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="delay_between_messages_min"
                    label="Min Delay (seconds)"
                    initialValue={10}
                  >
                    <InputNumber min={5} max={300} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="delay_between_messages_max"
                    label="Max Delay (seconds)"
                    initialValue={30}
                  >
                    <InputNumber min={10} max={600} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="stop_on_consecutive_failures"
                label="Stop after consecutive failures"
                initialValue={3}
              >
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="skip_sent_recipients"
                label="Skip already sent recipients"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Button
                type="primary"
                htmlType="submit"
                icon={<PlayCircleOutlined />}
                loading={loading}
                disabled={!uploadedFile || !fileValidation?.valid}
                block
                size="large"
              >
                Start Bulk Messaging
              </Button>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* Active Task Status */}
      {activeTask && taskProgress[activeTask] && (
        <Card title="Step 3: Messaging Progress" style={{ marginTop: 24 }}>
          <Row gutter={16}>
            <Col xs={24} lg={16}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <strong>Status:</strong> {getStatusTag(taskProgress[activeTask].status)}
                </div>

                <Progress
                  percent={taskProgress[activeTask].progress}
                  status={taskProgress[activeTask].status === 'failed' ? 'exception' : 'active'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />

                <Row gutter={16}>
                  <Col span={6}>
                    <Statistic
                      title="Total"
                      value={taskProgress[activeTask].total_recipients}
                      prefix={<UserOutlined />}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Sent"
                      value={taskProgress[activeTask].messages_sent}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Failed"
                      value={taskProgress[activeTask].messages_failed}
                      prefix={<CloseCircleOutlined />}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Consecutive Failures"
                      value={taskProgress[activeTask].consecutive_failures}
                      prefix={<ClockCircleOutlined />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Col>
                </Row>
              </Space>
            </Col>

            <Col xs={24} lg={8}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={() => handleStopTask(activeTask)}
                  block
                >
                  Stop Task
                </Button>

                <Button
                  icon={<EyeOutlined />}
                  onClick={() => handleViewResults(activeTask)}
                  block
                >
                  View Results
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>
      )}

      {/* Results Modal */}
      <Modal
        title="Bulk Messaging Results"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>
        ]}
      >
        {selectedTaskResults && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic title="Total Recipients" value={selectedTaskResults.total_recipients} />
              </Col>
              <Col span={6}>
                <Statistic title="Messages Sent" value={selectedTaskResults.messages_sent} />
              </Col>
              <Col span={6}>
                <Statistic title="Failed" value={selectedTaskResults.messages_failed} />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Success Rate"
                  value={selectedTaskResults.success_rate.toFixed(1)}
                  suffix="%"
                />
              </Col>
            </Row>

            <Divider />

            <Row gutter={16}>
              <Col span={12}>
                <h4>Successful Recipients ({selectedTaskResults.successful_recipients.length})</h4>
                <List
                  size="small"
                  dataSource={selectedTaskResults.successful_recipients.slice(0, 10)}
                  renderItem={(item) => (
                    <List.Item>
                      <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                      {item.recipient.full_name}
                    </List.Item>
                  )}
                />
              </Col>
              <Col span={12}>
                <h4>Failed Recipients ({selectedTaskResults.failed_recipients.length})</h4>
                <List
                  size="small"
                  dataSource={selectedTaskResults.failed_recipients.slice(0, 10)}
                  renderItem={(item) => (
                    <List.Item>
                      <CloseCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                      {item.recipient.full_name}
                      <br />
                      <small style={{ color: '#999' }}>{item.result.error}</small>
                    </List.Item>
                  )}
                />
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default BulkMessaging;
